.closecheckenv {
  background-color: #08d;
  border-radius: 12px;
  border: 0;
  box-sizing: border-box;
  color: #eee;
  cursor: pointer;
  font-size: 18px;
  height: 50px;
  margin-top: 38px;
  text-align: center;
  width: 100%;
}

.closecheckenv:active {
  background-color: #06b;
}

.checkenv-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

.checkenv-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  max-width: 80%;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  line-height: 1.2;
  max-height: 80vh;
  overflow-y: auto;
  padding: 10px;
}
.checkenv-container::-webkit-scrollbar {
  width: 10px;
}

.checkenv-container::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 10px;
}

.checkenv-container::-webkit-scrollbar-thumb {
  background: #bbb;
  border-radius: 10px;
}

.checkenv-container::-webkit-scrollbar-thumb:hover {
  background: #999;
}

.warning {
  background: #fff3cd;
  padding: 20px;
  border-radius: 5px;
  border: 1px solid #ffeeba;
}
.variables-container {
  max-height: 18vh;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  margin: 10px 0;
}

.variable {
  position: relative;
  padding: 10px;
  margin: 5px 0;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.variable .copy-button {
  position: absolute;
  right: 5px;
  top: 50%;
  transform: translateY(-50%);
  padding: 4px 8px;
  font-size: 12px;
  height: auto;
  width: auto;
}
