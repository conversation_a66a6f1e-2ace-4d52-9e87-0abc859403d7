.card {
  position: relative;
}

.ribbon {
  position: absolute;
  width: 160px;
  padding: 5px 0;
  background: rgb(21 160 210);
  color: #161998;
  font-weight: bold;
  text-align: center;
  transform: rotate(45deg);
  top: 20px;
  right: -35px;
  cursor: pointer;
  pointer-events: auto;
}
.ribbon:hover {
  color: transparent;
}
.ribbon:hover::after {
  content: attr(data-hover-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;

  display: flex;
  justify-content: center;
  align-items: center;

  color: rgb(227, 7, 7);
  font-weight: bold;
  pointer-events: none;
}

.hidden {
  display: none;
}
.ribbon-wrapper {
  position: absolute;
  top: 0;
  right: 0;
  overflow: hidden;
  width: 100px;
  height: 100px;
  pointer-events: none;
}

.copy-button {
  position: absolute;
  top: 0;
  right: 0;
  background-color: #04aa6d;
  border: none;
  color: white;
  padding: 8px 16px;
  text-align: center;
  text-decoration: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s ease;
  z-index: 10;
  width: 70px;
  height: 40px;
  white-space: nowrap;
  overflow: hidden;
}

.copy-button:hover {
  background-color: #057e47;
}

.code-block {
  position: relative;
  background-color: #f5f5f5;
  color: #333;
  padding: 20px;
  border-radius: 5px;
  font-family: "Courier New", Courier, monospace;
  font-size: 14px;
  white-space: pre-wrap;
  word-wrap: break-word;
  border: 1px solid #ddd;
}

.icon {
  width: 64px;
  height: 64px;
  background-size: contain;
  background-repeat: no-repeat;
}
.license-key {
  color: rgba(169, 167, 167, 0.6);
  max-width: 100%;
  max-height: 3.6em;
  overflow: auto;
  white-space: pre-wrap;
  word-break: break-all;
  font-family: monospace;
  padding: 4px 8px;
  border-radius: 4px;
  box-sizing: border-box;
  line-height: 1.2em;
  margin-top: 16px;

  scrollbar-width: none;
  -ms-overflow-style: none;
}
.license-key::-webkit-scrollbar {
  display: none;
}
.form-content {
  line-height: 1.8;
}
.jetbra-button {
  background-color: #04aa6d;
  border: none;
  color: white;
  padding: 8px 24px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  border-radius: 16px;
  box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
  transition-duration: 0.4s;
}

.jetbra-button:hover {
  background-color: #057e47;
  color: white;
}
.form {
  background-color: #15172b;
  border-radius: 20px;
  box-sizing: border-box;
  height: 500px;
  padding: 20px;
  width: 320px;
}

.title {
  color: #eee;
  font-family: sans-serif;
  font-size: 36px;
  font-weight: 600;
  margin-top: 30px;
}

.subtitle {
  color: #eee;
  font-family: sans-serif;
  font-size: 16px;
  font-weight: 600;
  margin-top: 10px;
}

.input-container {
  height: 50px;
  position: relative;
  width: 100%;
}

.ic1 {
  margin-top: 40px;
}

.ic2 {
  margin-top: 30px;
}

.input {
  background-color: #303245;
  border-radius: 12px;
  border: 0;
  box-sizing: border-box;
  color: #eee;
  font-size: 18px;
  height: 100%;
  outline: 0;
  padding: 4px 20px 0;
  width: 100%;
}

.cut {
  background-color: #15172b;
  border-radius: 10px;
  height: 20px;
  left: 20px;
  position: absolute;
  top: -20px;
  transform: translateY(0);
  transition: transform 200ms;
  width: 96px;
}

.cut-short {
  width: 80px;
}

.input:focus ~ .cut,
.input:not(:placeholder-shown) ~ .cut {
  transform: translateY(8px);
}

.placeholder {
  color: #65657b;
  font-family: sans-serif;
  left: 20px;
  line-height: 14px;
  pointer-events: none;
  position: absolute;
  transform-origin: 0 50%;
  transition: transform 200ms, color 200ms;
  top: 20px;
}

.input:focus ~ .placeholder,
.input:not(:placeholder-shown) ~ .placeholder {
  transform: translateY(-30px) translateX(10px) scale(0.75);
}

.input:not(:placeholder-shown) ~ .placeholder {
  color: #808097;
}

.input:focus ~ .placeholder {
  color: #dc2f55;
}

.submit {
  background-color: #08d;
  border-radius: 12px;
  border: 0;
  box-sizing: border-box;
  color: #eee;
  cursor: pointer;
  font-size: 18px;
  height: 50px;
  margin-top: 38px;
  text-align: center;
  width: 100%;
}

.submit:active {
  background-color: #06b;
}

.mask {
  transition: 0.2s;
  position: absolute;
  z-index: -1;
  width: 88%;
  height: 100%;
  bottom: 0;
  border-radius: 1.5rem;
  background-color: var(--grey-600);
  left: 50%;
  transform: translateX(-50%);
}

#mask {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 998;
  width: 100%;
  height: 100%;
  display: none;
  background-color: #000;
  opacity: 0.5;
  overflow: hidden;
}

#form {
  position: fixed;
  top: 20%;
  left: 40%;
  width: 40%;
  height: 500px;
  z-index: 999;
  display: none;
}

.form {
  background-color: #15172b;
  border-radius: 20px;
  box-sizing: border-box;
  height: 500px;
  padding: 20px;
  width: 320px;
}

.search-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.search-input {
  width: 300px;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 5px;
  font-size: 16px;
}
.code-block {
  background-color: #f5f5f5;
  color: #333;
  padding: 10px;
  border-radius: 5px;
  font-family: "Courier New", Courier, monospace;
  font-size: 14px;
  white-space: pre-wrap;
  word-wrap: break-word;
  border: 1px solid #ddd;
}

#mask-info {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 998;
  width: 100%;
  height: 100%;
  display: none;
  background-color: rgba(0, 0, 0, 0.5);
}

#form-info {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 800px;
  background-color: #1e1e2f;
  color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  z-index: 999;
  display: none;
  padding: 20px;
}
